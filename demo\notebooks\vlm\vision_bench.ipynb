"""This notebook is used to benchmark the VLM models. It uses a image-exclusive dataset.
Exactness is measured in 3 sync and async steps :
- 10 questions
- 100 questions
- 1000 questions
6 tests are performed and charted to show the performance of the model.
Async mode is used to observe the performance along the exactness and stress test the API.
"""

import os
import dspy
import logging
from typing import List, Dict, Any, Tuple, Optional
from openai import AzureOpenAI
import time
import sys
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), '../../..')))
from datasets import load_dataset
import glob
from src.api.live_api import FileUploader, DocumentSearch, Files
import tqdm
import json
import zlib
from tqdm.asyncio import tqdm as async_tqdm
from io import BytesIO
from src.api.live_api import AsyncDocumentSearch, Files
from PIL import Image

ds = load_dataset("Ryoo72/InfographicsVQA")

os.makedirs("images", exist_ok=True)
Image.MAX_IMAGE_PIXELS = None

all_samples = ds["test"]  # Get all samples instead of just 1000
image_crcs = {}  # CRC32 -> idx (to avoid duplicates)
idx_to_imagefile = {}  # idx of sample -> image file name and CRC

# Save unique images and mapping
for idx, sample in tqdm.tqdm(enumerate(all_samples), total=len(all_samples), desc=f"Processing {len(all_samples)} rows from the dataset"):
    img = sample['image']
    img_byte_arr = BytesIO()
    img.save(img_byte_arr, format=img.format)
    img_byte_arr = img_byte_arr.getvalue()
    crc = zlib.crc32(img_byte_arr)
    if crc in image_crcs:
        # Image already seen, reference existing image
        idx_to_imagefile[idx] = {
            "image_file": f"image_{image_crcs[crc]}.jpg",
            "crc": crc
        }
        continue
    # New image, save it
    image_crcs[crc] = idx
    img.save(f"images/image_{idx}.jpg")
    idx_to_imagefile[idx] = {
        "image_file": f"image_{idx}.jpg",
        "crc": crc
    }

print(f"{len(image_crcs)} images uniques téléchargées et sauvées dans le dossier 'images'")

# Preparation of JSON question/answer related to each image
qa_data_list = []
for idx, sample in tqdm.tqdm(enumerate(all_samples), total=len(all_samples), desc=f"Processing {len(all_samples)} questions and answers"):
    questions = sample['question']
    answers = sample['answers']
    image_info = idx_to_imagefile[idx]
    # If multiple questions, process all of them
    if isinstance(questions, list):
        for q, a in zip(questions, answers):
            qa_data_list.append({
                "image_file": image_info["image_file"],
                "crc": image_info["crc"],
                "question": q,
                "answers": a
            })
    else:
        qa_data_list.append({
            "image_file": image_info["image_file"],
            "crc": image_info["crc"],
            "question": questions,
            "answers": answers
        })

# Save JSON
with open("images/qa_data.json", "w", encoding="utf-8") as f:
    json.dump(qa_data_list, f, indent=4, ensure_ascii=False)

# Prepare the Judge with DSPy, it will answer with a Yes or a No if the provided answer by Trust is valid

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("VLM-Judge")

# Get Azure OpenAI configuration from environment variables
env_azure_api_key = os.getenv("AZURE_API_KEY")
env_azure_base_endpoint = os.getenv("AZURE_API_BASE")
env_azure_version = os.getenv("AZURE_API_VERSION")

# Create Azure OpenAI client for DSPy
azure_client = AzureOpenAI(
    api_key=env_azure_api_key,
    api_version=env_azure_version,
    azure_endpoint=env_azure_base_endpoint
)

# Define the DSPy module for the judge
class JudgeSignature(dspy.Signature):
    """Judge if a VLM's answer is correct based on expected answers."""
    question = dspy.InputField(desc="The question asked to the VLM")
    vlm_answer = dspy.InputField(desc="The answer provided by the VLM")
    expected_answers = dspy.InputField(desc="List of acceptable answers from the dataset")
    judgment = dspy.OutputField(desc="'YES' if the answer is correct, 'NO' otherwise")

class VLMJudge(dspy.Module):
    def __init__(self):
        super().__init__()
        self.judge = dspy.ChainOfThought(JudgeSignature)
        
    def forward(self, question: str, vlm_answer: str, expected_answers: List[str]) -> str:
        """
        Judge if the VLM's answer is correct based on the expected answers.
        
        Args:
            question: The question asked to the VLM
            vlm_answer: The answer provided by the VLM
            expected_answers: List of acceptable answers from the dataset
            
        Returns:
            "YES" if the answer is correct, "NO" otherwise
        """
        try:
            # Format expected answers as a string for better readability
            expected_str = ", ".join(expected_answers)
            
            # Use DSPy's structured approach to get the judgment
            response = self.judge(
                question=question,
                vlm_answer=vlm_answer,
                expected_answers=expected_str
            )
            
            # Extract and normalize the judgment
            judgment = response.judgment.strip().upper()
            
            # Ensure we only return YES or NO
            if judgment == "YES" or judgment == "NO":
                return judgment
            else:
                logger.warning(f"Judge returned invalid response: {judgment}. Defaulting to NO.")
                return "NO"
        except Exception as e:
            logger.error(f"Error in judge evaluation: {str(e)}")
            return "NO"  # Default to NO on error

# Configure DSPy with Azure OpenAI
try:
    # Set up the Azure OpenAI model for DSPy
    # For Azure OpenAI, we use the format "openai/deployment-name" and provide the Azure-specific parameters
    azure_lm = dspy.LM(
        model="azure/gpt-4.1-liveintel",  # Use the deployment name from your Azure account
        api_key=env_azure_api_key,
        api_base=env_azure_base_endpoint,
        api_version=env_azure_version
    )
    
    # Configure DSPy to use the Azure OpenAI model
    dspy.configure(lm=azure_lm)
    
    logger.info("DSPy configured successfully with Azure OpenAI")
except Exception as e:
    logger.error(f"Failed to configure DSPy with Azure OpenAI: {str(e)}")

# Create a batch-optimized judge function
def batch_judge(questions: List[str], vlm_answers: List[str], expected_answers: List[List[str]]) -> List[str]:
    """
    Process multiple question-answer pairs in batch.
    
    Args:
        questions: List of questions
        vlm_answers: List of answers from the VLM
        expected_answers: List of lists of acceptable answers
        
    Returns:
        List of judgments ("YES" or "NO")
    """
    if not (len(questions) == len(vlm_answers) == len(expected_answers)):
        logger.error(f"Mismatched input lengths: {len(questions)} questions, {len(vlm_answers)} VLM answers, {len(expected_answers)} expected answers")
        raise ValueError("Input lists must have the same length")
    
    judge = VLMJudge()
    results = []
    
    for i, (question, vlm_answer, expected) in enumerate(zip(questions, vlm_answers, expected_answers)):
        try:
            logger.info(f"Judging question {i+1}/{len(questions)}")
            result = judge(question, vlm_answer, expected)
            results.append(result)
        except Exception as e:
            logger.error(f"Error judging question {i+1}: {str(e)}")
            results.append("NO")  # Default to NO on error
    
    return results

# Example usage:
# judgments = batch_judge(
#     questions=["What color is the sky?", "What is 2+2?"],
#     vlm_answers=["Blue", "Four"],
#     expected_answers=[["Blue", "Azure"], ["4", "Four"]]
# )

API_KEY = "toKTSqr7.juIuOPGsW4V0AvSEon0KnQD3b76iNK0E"
BASE_URL = "https://trust.pprd.liveintelligence.orange-business.com/"

uploader = FileUploader(api_key=API_KEY, base_url=BASE_URL)

# Deactivate any existing sessions before starting
try:
    answ = uploader.deactivate_all_sessions()
    print(f"Tentative de réinitialisation des sessions existantes : {answ}")
except Exception as e:
    print(f"Erreur lors de la réinitialisation des sessions : {e}")

# Get list of image files to upload
image_files = [f for f in glob.glob("images/*") if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

# Upload files using the new batch processing function
# This implementation follows the established pattern:
# - Process files in batches of maximum 10 files
# - Use get_session_details() to monitor processing status
# - Wait for all documents in a batch to complete processing before starting the next batch
# - Properly clean up sessions between batches
print(f"Starting batch upload of {len(image_files)} images...")

try:
    upload_result = uploader.upload_files_in_batches(
        file_paths=image_files,
        batch_size=30,  # Maximum 10 files per batch as per established pattern
        max_wait_time=3600,  # 1 hour timeout per batch
        check_interval=30,  # Check status every 10 seconds
        collection_type="workspace",
        workspace_id=19
    )
    
    # Affichage des résultats d'upload
    print(f"\n=== Résumé Upload ===")
    print(f"Statut: {upload_result['status']}")
    print(f"Fichiers totaux: {upload_result['total_files']}")
    print(f"Uploads réussis: {upload_result['successful_uploads']}")
    print(f"Uploads échoués: {upload_result['failed_uploads']}")
    print(f"Lots traités: {upload_result['batches_processed']}")
    
    # Détails des uploads échoués
    if upload_result['failed_uploads'] > 0:
        print(f"\n=== Détails Uploads Échoués ===")
        for batch_detail in upload_result['details']:
            if batch_detail['failed_files']:
                print(f"Lot {batch_detail['batch_number']}:")
                for failed_file in batch_detail['failed_files']:
                    print(f"  - {failed_file['file_path']}: {failed_file['error']}")
    
except Exception as e:
    print(f"Erreur lors de l'upload par lots : {e}")
    raise

# Workspace "Giskard" sur la préprod
WORKSPACE_ID = 19

# Charger les questions/réponses
with open("images/qa_data.json", "r", encoding="utf-8") as f:
    qa_data = json.load(f)

# Regrouper les questions par image
image_to_questions = {}
for item in qa_data:
    img_file = item["image_file"]
    if img_file not in image_to_questions:
        image_to_questions[img_file] = []
    image_to_questions[img_file].append({
        "question": item["question"],
        "answers": item["answers"]
    })

# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID
files_api = Files(api_key=API_KEY, base_url=BASE_URL)
uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)
print(uploaded_files)

# Correction ici : on mappe les .jpg locaux aux .pdf côté API
filename_to_id = {}
for f in uploaded_files:
    if f["filename"].startswith("image_") and f["filename"].endswith(".pdf"):
        # On remplace .pdf par .jpg pour matcher les noms locaux
        local_name = f["filename"].replace(".pdf", ".jpg")
        filename_to_id[local_name] = f["id"]

# Initialiser l'API de recherche
doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)

results = []
response_times = []
start_time = time.time()

for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc="Interrogation des images"):
    file_id = filename_to_id.get(img_file)
    if not file_id:
        print(f"Image {img_file} introuvable côté API, ignorée.")
        continue

    # Pour chaque question associée à cette image
    for qa in qas:
        question = qa["question"]
        try:
            request_start = time.time()
            response = doc_search.execute(
                query=question,
                workspace_ids=[WORKSPACE_ID],
                file_ids=[file_id],  # On passe l'ID du fichier image
                tool="VisionDocumentSearch"
            )
            request_time = time.time() - request_start
            response_times.append(request_time)
        except Exception as e:
            response = {"error": str(e)}
            request_time = -1
            response_times.append(request_time)

        results.append({
            "image_file": img_file,
            "file_id": file_id,
            "question": question,
            "answers": qa["answers"],
            "api_response": response,
            "response_time": request_time
        })

total_time = time.time() - start_time
avg_response_time = sum([t for t in response_times if t > 0]) / len([t for t in response_times if t > 0])

# Ajouter les métadonnées de temps
metadata = {
    "response_times": response_times,
    "average_response_time": avg_response_time,
    "total_process_time": total_time,
    "total_requests": len(response_times),
    "failed_requests": len([t for t in response_times if t < 0])
}

final_output = {
    "results": results,
    "metadata": metadata
}

# Sauvegarde des résultats
with open("images/vision_results.json", "w", encoding="utf-8") as f:
    json.dump(final_output, f, indent=2, ensure_ascii=False)

print("Résultats sauvés dans images/vision_results.json")
print(f"\nStatistiques :")
print(f"Temps total : {total_time:.2f}s")
print(f"Temps moyen/requête : {avg_response_time:.2f}s")
print(f"Total requêtes : {len(response_times)}")
print(f"Requêtes échouées : {len([t for t in response_times if t < 0])}")

# Workspace "Giskard" sur la préprod
WORKSPACE_ID = 19

# Charger les questions/réponses
with open("images/qa_data.json", "r", encoding="utf-8") as f:
    qa_data = json.load(f)

# Regrouper les questions par image
image_to_questions = {}
for item in qa_data:
    img_file = item["image_file"]
    if img_file not in image_to_questions:
        image_to_questions[img_file] = []
    image_to_questions[img_file].append({
        "question": item["question"],
        "answers": item["answers"]
    })

# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID
files_api = Files(api_key=API_KEY, base_url=BASE_URL)
uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)
print(uploaded_files)

# Correction ici : on mappe les .jpg locaux aux .pdf côté API
filename_to_id = {}
for f in uploaded_files:
    if f["filename"].startswith("image_") and f["filename"].endswith(".pdf"):
        # On remplace .pdf par .jpg pour matcher les noms locaux
        local_name = f["filename"].replace(".pdf", ".jpg")
        filename_to_id[local_name] = f["id"]

# Initialiser l'API de recherche
doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)

results = []

# Listes pour le traitement par lots avec le juge DSPy
all_questions = []
all_vlm_answers = []
all_expected_answers = []

for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc="Interrogation des images ..."):
    file_id = filename_to_id.get(img_file)
    if not file_id:
        print(f"Image {img_file} introuvable côté API, ignorée.")
        continue

    # Pour chaque question associée à cette image
    for qa in qas:
        question = qa["question"]
        try:
            response = doc_search.execute(
                query=question,
                workspace_ids=[WORKSPACE_ID],
                file_ids=[file_id],  # On passe l'ID du fichier image
                tool="VisionDocumentSearch",
                model="alfred-4.1"
            )
            
            # Extraire la réponse du VLM
            vlm_answer = response.get("answer", "")
            if not vlm_answer and "error" not in response:
                # Essayer d'extraire la réponse d'une autre façon si le format est différent
                vlm_answer = response.get("content", "")
                if not vlm_answer:
                    # Dernier recours: convertir toute la réponse en chaîne
                    vlm_answer = str(response)
            
        except Exception as e:
            response = {"error": str(e)}
            # Pour les erreurs, utiliser une réponse par défaut
            vlm_answer = "ERROR: Unable to process question"
        
        # Toujours ajouter aux listes pour le traitement par lots (même en cas d'erreur)
        all_questions.append(question)
        all_vlm_answers.append(vlm_answer)
        all_expected_answers.append(qa["answers"] if isinstance(qa["answers"], list) else [qa["answers"]])

        results.append({
            "image_file": img_file,
            "file_id": file_id,
            "question": question,
            "answers": qa["answers"],
            "api_response": response
        })

# Vérification du nombre de questions traitées
print(f"\nQuestions traitées batch 2: {len(all_questions)}")
print(f"Résultats totaux: {len(results)}")

# Utiliser le juge DSPy pour évaluer les réponses en lot
print(f"\nÉvaluation de {len(all_questions)} réponses avec juge DSPy...")
try:
    judgments = batch_judge(all_questions, all_vlm_answers, all_expected_answers)
    
    # Ajouter les jugements aux résultats
    judgment_index = 0
    for i, result in enumerate(results):
        if "error" not in result["api_response"]:
            if judgment_index < len(judgments):
                result["judge_verdict"] = judgments[judgment_index]
                judgment_index += 1
            else:
                result["judge_verdict"] = "NO_JUDGMENT"
        else:
            result["judge_verdict"] = "ERROR"
    
    # Calculer les statistiques
    correct_answers = judgments.count("YES")
    total_judged = len(judgments)
    accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0
    
    print(f"\nRésultats évaluation:")
    print(f"Réponses correctes: {correct_answers}/{total_judged} ({accuracy:.2f}%)")
    
except Exception as e:
    print(f"Erreur lors de l'évaluation par le juge DSPy: {str(e)}")

# Sauvegarde des résultats
with open("images/results_batch_2.json", "w", encoding="utf-8") as f:
    json.dump(results, f, indent=2, ensure_ascii=False)

print("\nRésultats sauvés dans images/results_batch_2.json")

# Workspace "Giskard" sur la préprod
WORKSPACE_ID = 19

# Charger les questions/réponses
with open("images/qa_data.json", "r", encoding="utf-8") as f:
    qa_data = json.load(f)

# Limiter à X premières lignes du fichier data
# qa_data = qa_data[:100]

# Regrouper les questions par image
image_to_questions = {}
for item in qa_data:
    img_file = item["image_file"]
    if img_file not in image_to_questions:
        image_to_questions[img_file] = []
    image_to_questions[img_file].append({
        "question": item["question"],
        "answers": item["answers"]
    })

# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID
files_api = Files(api_key=API_KEY, base_url=BASE_URL)
uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)
print(uploaded_files)

# Correction ici : on mappe les .jpg locaux aux .pdf côté API
filename_to_id = {}
for f in uploaded_files:
    if f["filename"].startswith("image_") and f["filename"].endswith(".pdf"):
        # On remplace .pdf par .jpg pour matcher les noms locaux
        local_name = f["filename"].replace(".pdf", ".jpg")
        filename_to_id[local_name] = f["id"]

# Initialiser l'API de recherche
doc_search = DocumentSearch(api_key=API_KEY, base_url=BASE_URL)

results_batch3 = []
all_questions_batch3 = []
all_vlm_answers_batch3 = []
all_expected_answers_batch3 = []
response_times_batch3 = []

start_time = time.time()

for img_file, qas in tqdm.tqdm(image_to_questions.items(), desc="Interrogation globale des images ..."):
    for qa in qas:
        question = qa["question"]
        try:
            request_start = time.time()
            response = doc_search.execute(
                query=question,
                workspace_ids=[WORKSPACE_ID],
                tool="DocumentSearch",  # Utilisation du nouveau tool
                model="alfred-4.1"
            )
            request_time = time.time() - request_start
            response_times_batch3.append(request_time)

            # Extraire la réponse du VLM
            vlm_answer = response.get("answer", "")
            if not vlm_answer and "error" not in response:
                vlm_answer = response.get("content", "")
                if not vlm_answer:
                    vlm_answer = str(response)

        except Exception as e:
            response = {"error": str(e)}
            request_time = -1
            response_times_batch3.append(request_time)
            vlm_answer = "ERROR: Unable to process question"

        # Toujours ajouter aux listes pour le traitement par lots (même en cas d'erreur)
        all_questions_batch3.append(question)
        all_vlm_answers_batch3.append(vlm_answer)
        all_expected_answers_batch3.append(qa["answers"] if isinstance(qa["answers"], list) else [qa["answers"]])

        # Récupérer l'ID de l'image originale pour référence
        original_file_id = filename_to_id.get(img_file)

        # Extraire les noms des documents trouvés par le système (ex: "image_444")
        found_image_names = []
        if "documents" in response:
            for doc in response.get("documents", []):
                # On récupère le nom du document (sans extension)
                name = doc.get("name", "")
                found_image_names.append(name)

        results_batch3.append({
            "image_file": img_file,
            "original_file_id": original_file_id,
            "found_image_names": found_image_names,
            "question": question,
            "answers": qa["answers"],
            "api_response": response,
            "response_time": request_time
        })

total_time = time.time() - start_time
avg_response_time = sum([t for t in response_times_batch3 if t > 0]) / len([t for t in response_times_batch3 if t > 0]) if len([t for t in response_times_batch3 if t > 0]) > 0 else 0

# Vérification du nombre de questions traitées
print(f"\nQuestions traitées batch 3: {len(all_questions_batch3)}")
print(f"Résultats totaux: {len(results_batch3)}")

# Utiliser le juge DSPy pour évaluer les réponses en lot
print(f"\nÉvaluation de {len(all_questions_batch3)} réponses avec juge DSPy...")
try:
    judgments_batch3 = batch_judge(all_questions_batch3, all_vlm_answers_batch3, all_expected_answers_batch3)

    # Ajouter les jugements aux résultats
    judgment_index = 0
    for i, result in enumerate(results_batch3):
        if "error" not in result["api_response"]:
            if judgment_index < len(judgments_batch3):
                result["judge_verdict"] = judgments_batch3[judgment_index]
                judgment_index += 1
            else:
                result["judge_verdict"] = "NO_JUDGMENT"
        else:
            result["judge_verdict"] = "ERROR"

    # Calculer les statistiques
    correct_answers = judgments_batch3.count("YES")
    total_judged = len(judgments_batch3)
    accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0

    # Calculer le taux de réussite pour trouver la bonne image
    correct_image_found = 0
    for result in results_batch3:
        # On considère que le document cible est trouvé si le nom de l'image (sans extension) est dans found_image_names
        target_name = result["image_file"].replace(".jpg", "").replace(".jpeg", "").replace(".png", "")
        if target_name in result["found_image_names"]:
            correct_image_found += 1

    image_accuracy = (correct_image_found / len(results_batch3)) * 100 if len(results_batch3) > 0 else 0

    # Affichage des statistiques principales
    print(f"\nRésultats évaluation batch 3:")
    print(f"Réponses correctes (DSPy Judge): {correct_answers}/{total_judged} ({accuracy:.2f}%)")
    print(f"Bon document trouvé: {correct_image_found}/{len(results_batch3)} ({image_accuracy:.2f}%)")
    print(f"Temps moyen/requête: {avg_response_time:.2f}s")

except Exception as e:
    print(f"Erreur lors de l'évaluation par le juge DSPy: {str(e)}")

# Sauvegarde des résultats
final_output = {
    "results": results_batch3,
    "metadata": {
        "average_response_time": avg_response_time,
        "total_process_time": total_time,
        "total_requests": len(response_times_batch3),
        "answer_accuracy": accuracy if 'accuracy' in locals() else None,
        "image_search_accuracy": image_accuracy if 'image_accuracy' in locals() else None
    }
}

with open("images/results_batch_3.json", "w", encoding="utf-8") as f:
    json.dump(final_output, f, indent=2, ensure_ascii=False)

print("\nRésultats sauvés dans images/results_batch_3.json")

# Workspace "Giskard" sur la préprod
WORKSPACE_ID = 19

# Charger les questions/réponses
with open("images/qa_data.json", "r", encoding="utf-8") as f:
    qa_data = json.load(f)

# Regrouper les questions par image
image_to_questions = {}
for item in qa_data:
    img_file = item["image_file"]
    if img_file not in image_to_questions:
        image_to_questions[img_file] = []
    image_to_questions[img_file].append({
        "question": item["question"],
        "answers": item["answers"]
    })

# Récupérer la liste des fichiers uploadés et faire la correspondance nom <-> ID
files_api = Files(api_key=API_KEY, base_url=BASE_URL)
uploaded_files = files_api.execute(workspace_scope=WORKSPACE_ID)
filename_to_id = {}
for f in uploaded_files:
    if f["filename"].startswith("image_") and f["filename"].endswith(".pdf"):
        local_name = f["filename"].replace(".pdf", ".jpg")
        filename_to_id[local_name] = f["id"]

# Préparer les requêtes asynchrones avec le nom du fichier cible
async_requests = []
target_image_names = []  # Pour le calcul du taux de bons documents trouvés
for img_file, qas in image_to_questions.items():
    for qa in qas:
        req = {
            "query": qa["question"],
            "workspace_ids": [WORKSPACE_ID],
            "tool": "DocumentSearch",
            "model": "alfred-4.1"
        }
        async_requests.append(req)
        # On stocke le nom cible sans extension pour la comparaison
        target_image_names.append(img_file.replace(".jpg", "").replace(".jpeg", "").replace(".png", ""))

# Fonction principale asynchrone
async def run_async_benchmark():
    async_api = AsyncDocumentSearch(api_key=API_KEY, base_url=BASE_URL)
    results = []
    all_questions = []
    all_vlm_answers = []
    all_expected_answers = []
    found_image_names_list = []  # Pour stocker les documents trouvés par l'API

    def progress_callback(done, total):
        if done % 10 == 0 or done == total:
            print(f"{done}/{total} requêtes traitées...")

    start_time = time.time()
    batch_results = await async_api.execute_batch(
        requests_data=async_requests,
        max_concurrent=10,
        progress_callback=progress_callback
    )
    total_time = time.time() - start_time

    for i, (req, res) in enumerate(zip(async_requests, batch_results)):
        question = req["query"]
        answers = qa_data[i]["answers"] if i < len(qa_data) else []
        api_response = res["result"] if res["error"] is None else {"error": res["error"]}
        vlm_answer = ""
        found_image_names = []
        if "error" not in api_response:
            vlm_answer = api_response.get("answer", "") or api_response.get("content", "") or str(api_response)
            # Extraction des documents trouvés
            if "documents" in api_response:
                for doc in api_response.get("documents", []):
                    name = doc.get("name", "")
                    found_image_names.append(name)
        else:
            vlm_answer = "ERROR: Unable to process question"
        found_image_names_list.append(found_image_names)

        results.append({
            "image_file": None,  # Peut être retrouvé si besoin
            "question": question,
            "answers": answers,
            "api_response": api_response,
            "vlm_answer": vlm_answer,
            "found_image_names": found_image_names
        })
        all_questions.append(question)
        all_vlm_answers.append(vlm_answer)
        all_expected_answers.append(answers if isinstance(answers, list) else [answers])

    # Évaluation avec le juge DSPy
    print(f"\nÉvaluation de {len(all_questions)} réponses avec juge DSPy...")
    try:
        judgments = batch_judge(all_questions, all_vlm_answers, all_expected_answers)
        for i, result in enumerate(results):
            if "error" not in result["api_response"]:
                result["judge_verdict"] = judgments[i] if i < len(judgments) else "NO_JUDGMENT"
            else:
                result["judge_verdict"] = "ERROR"
        correct_answers = judgments.count("YES")
        total_judged = len(judgments)
        accuracy = (correct_answers / total_judged) * 100 if total_judged > 0 else 0
        print(f"Réponses correctes (DSPy Judge): {correct_answers}/{total_judged} ({accuracy:.2f}%)")
    except Exception as e:
        print(f"Erreur lors de l'évaluation par le juge DSPy: {str(e)}")
        accuracy = None

    # Calcul du taux de bons documents trouvés
    correct_image_found = 0
    for i, found_names in enumerate(found_image_names_list):
        target_name = target_image_names[i]
        if target_name in found_names:
            correct_image_found += 1
    image_accuracy = (correct_image_found / len(found_image_names_list)) * 100 if len(found_image_names_list) > 0 else 0
    print(f"Bons documents trouvés: {correct_image_found}/{len(found_image_names_list)} ({image_accuracy:.2f}%)")

    # Sauvegarde des résultats
    final_output = {
        "results": results,
        "metadata": {
            "total_process_time": total_time,
            "total_requests": len(results),
            "answer_accuracy": accuracy,
            "image_search_accuracy": image_accuracy
        }
    }
    with open("images/results_batch_5_async.json", "w", encoding="utf-8") as f:
        json.dump(final_output, f, indent=2, ensure_ascii=False)
    print("\nRésultats sauvés dans images/results_batch_5_async.json")

# Await run
await run_async_benchmark()